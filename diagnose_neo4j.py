"""
Neo4j连接诊断工具
"""
from config import Config
from neo4j import GraphDatabase
import socket
import sys

def check_neo4j_service():
    """检查Neo4j服务是否运行"""
    print("🔍 检查Neo4j服务状态...")
    
    # 解析URI
    uri_parts = Config.NEO4J_URI.replace("neo4j://", "").split(":")
    host = uri_parts[0]
    port = int(uri_parts[1]) if len(uri_parts) > 1 else 7687
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✓ Neo4j服务在 {host}:{port} 上运行")
            return True
        else:
            print(f"❌ 无法连接到 {host}:{port}")
            return False
    except Exception as e:
        print(f"❌ 网络检查失败: {e}")
        return False

def test_different_auth():
    """尝试不同的认证方式"""
    print("\n🔍 尝试不同的认证配置...")
    
    # 常见的默认密码
    common_passwords = ["neo4j", "password", "admin", Config.NEO4J_PASSWORD]
    
    for password in common_passwords:
        if not password:
            continue
            
        print(f"尝试密码: {'*' * len(password)}")
        try:
            driver = GraphDatabase.driver(
                Config.NEO4J_URI,
                auth=(Config.NEO4J_USER, password)
            )
            
            with driver.session() as session:
                result = session.run("RETURN 1 as test")
                record = result.single()
                if record:
                    print(f"✓ 认证成功！正确的密码是: {'*' * len(password)}")
                    driver.close()
                    return password
                    
        except Exception as e:
            print(f"❌ 认证失败: {str(e)[:100]}...")
            
        try:
            driver.close()
        except:
            pass
    
    return None

def show_connection_info():
    """显示连接信息"""
    print("\n📋 当前连接配置:")
    print(f"URI: {Config.NEO4J_URI}")
    print(f"用户: {Config.NEO4J_USER}")
    print(f"密码: {'*' * len(Config.NEO4J_PASSWORD) if Config.NEO4J_PASSWORD else 'None'}")
    print(f"数据库: {Config.NEO4J_DATABASE}")

def show_troubleshooting_tips():
    """显示故障排除建议"""
    print("\n🔧 故障排除建议:")
    print("1. 确认Neo4j Desktop或服务正在运行")
    print("2. 检查Neo4j Browser是否可以连接 (http://localhost:7474)")
    print("3. 确认密码是否正确")
    print("4. 如果是新安装，默认密码可能是 'neo4j'")
    print("5. 检查防火墙是否阻止了7687端口")
    print("6. 尝试重启Neo4j服务")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 Neo4j连接诊断工具")
    print("=" * 60)
    
    show_connection_info()
    
    # 检查服务状态
    service_running = check_neo4j_service()
    
    if not service_running:
        print("\n❌ Neo4j服务未运行或无法访问")
        show_troubleshooting_tips()
        return 1
    
    # 尝试认证
    correct_password = test_different_auth()
    
    if correct_password:
        print(f"\n🎉 找到正确的密码！")
        if correct_password != Config.NEO4J_PASSWORD:
            print(f"请更新.env文件中的NEO4J_PASSWORD为: {correct_password}")
        return 0
    else:
        print("\n❌ 所有认证尝试都失败了")
        show_troubleshooting_tips()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
