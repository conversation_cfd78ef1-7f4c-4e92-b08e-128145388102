"""
配置文件 - 管理Neo4j和OpenAI连接设置
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置类"""
    
    # Neo4j 配置
    NEO4J_URI = os.getenv("NEO4J_URI", "neo4j://127.0.0.1:7687")
    NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
    NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")
    NEO4J_DATABASE = os.getenv("NEO4J_DATABASE", "neo4j")
    
    # OpenAI 配置
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    
    # 实例配置
    INSTANCE_NAME = os.getenv("INSTANCE_NAME", "gyrw-kg")
    NEO4J_VERSION = os.getenv("NEO4J_VERSION", "2025.05.0")
    
    # 文件路径
    ENTITIES_WHITELIST = "whitelists/entities.csv"
    LABELS_WHITELIST = "whitelists/labels.csv"
    RELATIONS_WHITELIST = "whitelists/relations.csv"
    
    # LLM 配置
    MODEL_NAME = "gpt-4o-mini"
    MAX_TOKENS = 4000
    TEMPERATURE = 0
    
    # 分块配置
    MAX_CHUNK_SIZE = 2800  # 约700 tokens
    
    @classmethod
    def validate(cls):
        """验证必要的配置是否存在"""
        if not cls.NEO4J_PASSWORD:
            raise ValueError("NEO4J_PASSWORD 环境变量未设置")
        if not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY 环境变量未设置")
        
        print(f"✓ 实例名称: {cls.INSTANCE_NAME}")
        print(f"✓ Neo4j版本: {cls.NEO4J_VERSION}")
        print(f"✓ Neo4j URI: {cls.NEO4J_URI}")
        print(f"✓ Neo4j用户: {cls.NEO4J_USER}")
        print(f"✓ Neo4j数据库: {cls.NEO4J_DATABASE}")
        print(f"✓ OpenAI模型: {cls.MODEL_NAME}")
        print("✓ 所有配置验证通过")

if __name__ == "__main__":
    Config.validate()
