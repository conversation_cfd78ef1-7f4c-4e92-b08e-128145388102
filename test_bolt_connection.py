"""
尝试不同的连接协议
"""
import os
from dotenv import load_dotenv
from neo4j import GraphDatabase

load_dotenv()

def test_different_protocols():
    user = os.getenv("NEO4J_USER")
    password = os.getenv("NEO4J_PASSWORD")
    
    # 尝试不同的URI格式
    uris = [
        "bolt://127.0.0.1:7687",
        "neo4j://127.0.0.1:7687", 
        "bolt://localhost:7687",
        "neo4j://localhost:7687"
    ]
    
    for uri in uris:
        print(f"\n🔍 测试 {uri}")
        try:
            driver = GraphDatabase.driver(uri, auth=(user, password))
            
            with driver.session() as session:
                result = session.run("RETURN 'Hello!' as message")
                record = result.single()
                print(f"✓ 成功: {record['message']}")
                
            driver.close()
            return uri
            
        except Exception as e:
            print(f"❌ 失败: {str(e)[:100]}...")
    
    return None

def test_without_database():
    """尝试不指定数据库"""
    user = os.getenv("NEO4J_USER")
    password = os.getenv("NEO4J_PASSWORD")
    
    print(f"\n🔍 测试不指定数据库")
    try:
        driver = GraphDatabase.driver("bolt://127.0.0.1:7687", auth=(user, password))
        
        with driver.session() as session:  # 不指定database
            result = session.run("RETURN 'Hello!' as message")
            record = result.single()
            print(f"✓ 成功: {record['message']}")
            
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ 失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🔍 Neo4j连接协议测试")
    print("=" * 50)
    
    # 测试不同协议
    working_uri = test_different_protocols()
    
    if working_uri:
        print(f"\n🎉 找到可用的URI: {working_uri}")
    else:
        print("\n❌ 所有URI都失败了")
        
    # 测试不指定数据库
    no_db_works = test_without_database()
    
    if no_db_works:
        print("\n💡 建议：不指定数据库名称可能有效")
