"""
仅测试OpenAI连接
"""
from config import Config
from openai import OpenAI

def test_openai_detailed():
    """详细测试OpenAI功能"""
    print("🔍 测试OpenAI连接和功能...")
    
    try:
        client = OpenAI(api_key=Config.OPENAI_API_KEY)
        
        # 测试基本聊天
        print("1. 测试基本聊天...")
        response = client.chat.completions.create(
            model=Config.MODEL_NAME,
            messages=[{"role": "user", "content": "你好！"}],
            max_tokens=50
        )
        print(f"✓ 基本聊天成功: {response.choices[0].message.content.strip()}")
        
        # 测试JSON模式
        print("\n2. 测试JSON模式...")
        json_response = client.chat.completions.create(
            model=Config.MODEL_NAME,
            response_format={"type": "json_object"},
            messages=[{
                "role": "user", 
                "content": "请返回一个JSON对象，包含name和description字段，描述'函数'这个概念"
            }],
            max_tokens=100
        )
        print(f"✓ JSON模式成功: {json_response.choices[0].message.content.strip()}")
        
        # 测试实体抽取示例
        print("\n3. 测试实体抽取...")
        extract_response = client.chat.completions.create(
            model=Config.MODEL_NAME,
            response_format={"type": "json_object"},
            messages=[{
                "role": "system",
                "content": "你是一个信息抽取助手。从文本中抽取实体和关系，返回JSON格式。"
            }, {
                "role": "user",
                "content": "函数可以接收参数，参数有实参和形参两种类型。请抽取实体和关系。"
            }],
            max_tokens=200
        )
        print(f"✓ 实体抽取成功: {extract_response.choices[0].message.content.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAI测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 OpenAI功能测试")
    print("=" * 60)
    
    success = test_openai_detailed()
    
    if success:
        print("\n🎉 OpenAI连接和功能测试全部通过！")
        print("\n📝 Neo4j连接问题解决方案:")
        print("1. 打开Neo4j Browser: http://localhost:7474")
        print("2. 使用正确的用户名和密码登录")
        print("3. 记录正确的密码")
        print("4. 更新.env文件中的NEO4J_PASSWORD")
        print("5. 重新运行 python test_connections.py")
        return 0
    else:
        print("\n❌ OpenAI连接有问题，请检查API密钥")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)
