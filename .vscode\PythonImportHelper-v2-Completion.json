[{"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "importlib", "kind": 6, "isExtraImport": true, "importPath": "importlib", "description": "importlib", "detail": "importlib", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "Config", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "Config", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "Config", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "GraphDatabase", "importPath": "neo4j", "description": "neo4j", "isExtraImport": true, "detail": "neo4j", "documentation": {}}, {"label": "socket", "kind": 6, "isExtraImport": true, "importPath": "socket", "description": "socket", "detail": "socket", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "clear_environment_cache", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def clear_environment_cache():\n    \"\"\"清除环境变量缓存\"\"\"\n    print(\"🧹 清除环境变量缓存...\")\n    # 清除相关的环境变量\n    env_vars = ['NEO4J_URI', 'NEO4J_USER', 'NEO4J_PASSWORD', 'NEO4J_DATABASE', 'OPENAI_API_KEY']\n    for var in env_vars:\n        if var in os.environ:\n            del os.environ[var]\n            print(f\"✓ 清除 {var}\")\ndef clear_module_cache():", "detail": "clear_cache", "documentation": {}}, {"label": "clear_module_cache", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def clear_module_cache():\n    \"\"\"清除Python模块缓存\"\"\"\n    print(\"\\n🧹 清除Python模块缓存...\")\n    modules_to_clear = ['config', 'dotenv']\n    for module_name in modules_to_clear:\n        if module_name in sys.modules:\n            del sys.modules[module_name]\n            print(f\"✓ 清除模块 {module_name}\")\ndef reload_config():\n    \"\"\"重新加载配置\"\"\"", "detail": "clear_cache", "documentation": {}}, {"label": "reload_config", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def reload_config():\n    \"\"\"重新加载配置\"\"\"\n    print(\"\\n🔄 重新加载配置...\")\n    try:\n        # 重新导入dotenv和config\n        from dotenv import load_dotenv\n        load_dotenv(override=True)  # 强制重新加载\n        from config import Config\n        Config.validate()\n        print(\"✓ 配置重新加载成功\")", "detail": "clear_cache", "documentation": {}}, {"label": "test_fresh_connection", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def test_fresh_connection():\n    \"\"\"使用新配置测试连接\"\"\"\n    print(\"\\n🔍 使用新配置测试连接...\")\n    try:\n        from neo4j import GraphDatabase\n        from config import Config\n        print(f\"URI: {Config.NEO4J_URI}\")\n        print(f\"用户: {Config.NEO4J_USER}\")\n        print(f\"密码: {'*' * len(Config.NEO4J_PASSWORD) if Config.NEO4J_PASSWORD else 'None'}\")\n        driver = GraphDatabase.driver(", "detail": "clear_cache", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "clear_cache", "description": "clear_cache", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\" * 60)\n    print(\"🧹 清除密码缓存并重新测试连接\")\n    print(\"=\" * 60)\n    # 清除缓存\n    clear_environment_cache()\n    clear_module_cache()\n    # 重新加载配置\n    config_ok = reload_config()", "detail": "clear_cache", "documentation": {}}, {"label": "Config", "kind": 6, "importPath": "config", "description": "config", "peekOfCode": "class Config:\n    \"\"\"配置类\"\"\"\n    # Neo4j 配置\n    NEO4J_URI = os.getenv(\"NEO4J_URI\", \"neo4j://127.0.0.1:7687\")\n    NEO4J_USER = os.getenv(\"NEO4J_USER\", \"neo4j\")\n    NEO4J_PASSWORD = os.getenv(\"NEO4J_PASSWORD\")\n    NEO4J_DATABASE = os.getenv(\"NEO4J_DATABASE\", \"neo4j\")\n    # OpenAI 配置\n    OPENAI_API_KEY = os.getenv(\"OPENAI_API_KEY\")\n    # 实例配置", "detail": "config", "documentation": {}}, {"label": "check_neo4j_service", "kind": 2, "importPath": "diagnose_neo4j", "description": "diagnose_neo4j", "peekOfCode": "def check_neo4j_service():\n    \"\"\"检查Neo4j服务是否运行\"\"\"\n    print(\"🔍 检查Neo4j服务状态...\")\n    # 解析URI\n    uri_parts = Config.NEO4J_URI.replace(\"neo4j://\", \"\").split(\":\")\n    host = uri_parts[0]\n    port = int(uri_parts[1]) if len(uri_parts) > 1 else 7687\n    try:\n        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n        sock.settimeout(5)", "detail": "diagnose_neo4j", "documentation": {}}, {"label": "test_different_auth", "kind": 2, "importPath": "diagnose_neo4j", "description": "diagnose_neo4j", "peekOfCode": "def test_different_auth():\n    \"\"\"尝试不同的认证方式\"\"\"\n    print(\"\\n🔍 尝试不同的认证配置...\")\n    # 常见的默认密码\n    common_passwords = [\"neo4j\", \"password\", \"admin\", Config.NEO4J_PASSWORD]\n    for password in common_passwords:\n        if not password:\n            continue\n        print(f\"尝试密码: {'*' * len(password)}\")\n        try:", "detail": "diagnose_neo4j", "documentation": {}}, {"label": "show_connection_info", "kind": 2, "importPath": "diagnose_neo4j", "description": "diagnose_neo4j", "peekOfCode": "def show_connection_info():\n    \"\"\"显示连接信息\"\"\"\n    print(\"\\n📋 当前连接配置:\")\n    print(f\"URI: {Config.NEO4J_URI}\")\n    print(f\"用户: {Config.NEO4J_USER}\")\n    print(f\"密码: {'*' * len(Config.NEO4J_PASSWORD) if Config.NEO4J_PASSWORD else 'None'}\")\n    print(f\"数据库: {Config.NEO4J_DATABASE}\")\ndef show_troubleshooting_tips():\n    \"\"\"显示故障排除建议\"\"\"\n    print(\"\\n🔧 故障排除建议:\")", "detail": "diagnose_neo4j", "documentation": {}}, {"label": "show_troubleshooting_tips", "kind": 2, "importPath": "diagnose_neo4j", "description": "diagnose_neo4j", "peekOfCode": "def show_troubleshooting_tips():\n    \"\"\"显示故障排除建议\"\"\"\n    print(\"\\n🔧 故障排除建议:\")\n    print(\"1. 确认Neo4j Desktop或服务正在运行\")\n    print(\"2. 检查Neo4j Browser是否可以连接 (http://localhost:7474)\")\n    print(\"3. 确认密码是否正确\")\n    print(\"4. 如果是新安装，默认密码可能是 'neo4j'\")\n    print(\"5. 检查防火墙是否阻止了7687端口\")\n    print(\"6. 尝试重启Neo4j服务\")\ndef main():", "detail": "diagnose_neo4j", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "diagnose_neo4j", "description": "diagnose_neo4j", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\" * 60)\n    print(\"🔍 Neo4j连接诊断工具\")\n    print(\"=\" * 60)\n    show_connection_info()\n    # 检查服务状态\n    service_running = check_neo4j_service()\n    if not service_running:\n        print(\"\\n❌ Neo4j服务未运行或无法访问\")", "detail": "diagnose_neo4j", "documentation": {}}, {"label": "test_neo4j", "kind": 2, "importPath": "simple_neo4j_test", "description": "simple_neo4j_test", "peekOfCode": "def test_neo4j():\n    uri = os.getenv(\"NEO4J_URI\")\n    user = os.getenv(\"NEO4J_USER\") \n    password = os.getenv(\"NEO4J_PASSWORD\")\n    database = os.getenv(\"NEO4J_DATABASE\")\n    print(f\"URI: {uri}\")\n    print(f\"用户: {user}\")\n    print(f\"密码: {'*' * len(password) if password else 'None'}\")\n    print(f\"数据库: {database}\")\n    print()", "detail": "simple_neo4j_test", "documentation": {}}, {"label": "test_different_protocols", "kind": 2, "importPath": "test_bolt_connection", "description": "test_bolt_connection", "peekOfCode": "def test_different_protocols():\n    user = os.getenv(\"NEO4J_USER\")\n    password = os.getenv(\"NEO4J_PASSWORD\")\n    # 尝试不同的URI格式\n    uris = [\n        \"bolt://127.0.0.1:7687\",\n        \"neo4j://127.0.0.1:7687\", \n        \"bolt://localhost:7687\",\n        \"neo4j://localhost:7687\"\n    ]", "detail": "test_bolt_connection", "documentation": {}}, {"label": "test_without_database", "kind": 2, "importPath": "test_bolt_connection", "description": "test_bolt_connection", "peekOfCode": "def test_without_database():\n    \"\"\"尝试不指定数据库\"\"\"\n    user = os.getenv(\"NEO4J_USER\")\n    password = os.getenv(\"NEO4J_PASSWORD\")\n    print(f\"\\n🔍 测试不指定数据库\")\n    try:\n        driver = GraphDatabase.driver(\"bolt://127.0.0.1:7687\", auth=(user, password))\n        with driver.session() as session:  # 不指定database\n            result = session.run(\"RETURN 'Hello!' as message\")\n            record = result.single()", "detail": "test_bolt_connection", "documentation": {}}, {"label": "test_neo4j_connection", "kind": 2, "importPath": "test_connections", "description": "test_connections", "peekOfCode": "def test_neo4j_connection():\n    \"\"\"测试Neo4j连接\"\"\"\n    print(\"🔍 测试Neo4j连接...\")\n    try:\n        driver = GraphDatabase.driver(\n            Config.NEO4J_URI,\n            auth=(Config.NEO4J_USER, Config.NEO4J_PASSWORD)\n        )\n        with driver.session(database=Config.NEO4J_DATABASE) as session:\n            result = session.run(\"RETURN 'Hello Neo4j!' as message\")", "detail": "test_connections", "documentation": {}}, {"label": "test_openai_connection", "kind": 2, "importPath": "test_connections", "description": "test_connections", "peekOfCode": "def test_openai_connection():\n    \"\"\"测试OpenAI连接\"\"\"\n    print(\"\\n🔍 测试OpenAI连接...\")\n    try:\n        client = OpenAI(api_key=Config.OPENAI_API_KEY)\n        # 简单的测试请求\n        response = client.chat.completions.create(\n            model=Config.MODEL_NAME,\n            messages=[{\"role\": \"user\", \"content\": \"Hello!\"}],\n            max_tokens=10", "detail": "test_connections", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_connections", "description": "test_connections", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\" * 50)\n    print(\"🚀 gyrw-kg 环境连接测试\")\n    print(\"=\" * 50)\n    # 验证配置\n    try:\n        Config.validate()\n    except ValueError as e:\n        print(f\"❌ 配置错误: {e}\")", "detail": "test_connections", "documentation": {}}, {"label": "test_openai_detailed", "kind": 2, "importPath": "test_openai_only", "description": "test_openai_only", "peekOfCode": "def test_openai_detailed():\n    \"\"\"详细测试OpenAI功能\"\"\"\n    print(\"🔍 测试OpenAI连接和功能...\")\n    try:\n        client = OpenAI(api_key=Config.OPENAI_API_KEY)\n        # 测试基本聊天\n        print(\"1. 测试基本聊天...\")\n        response = client.chat.completions.create(\n            model=Config.MODEL_NAME,\n            messages=[{\"role\": \"user\", \"content\": \"你好！\"}],", "detail": "test_openai_only", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_openai_only", "description": "test_openai_only", "peekOfCode": "def main():\n    \"\"\"主函数\"\"\"\n    print(\"=\" * 60)\n    print(\"🤖 OpenAI功能测试\")\n    print(\"=\" * 60)\n    success = test_openai_detailed()\n    if success:\n        print(\"\\n🎉 OpenAI连接和功能测试全部通过！\")\n        print(\"\\n📝 Neo4j连接问题解决方案:\")\n        print(\"1. 打开Neo4j Browser: http://localhost:7474\")", "detail": "test_openai_only", "documentation": {}}]