"""
测试Neo4j和OpenAI连接
"""
from config import Config
from neo4j import GraphDatabase
from openai import OpenAI
import sys

def test_neo4j_connection():
    """测试Neo4j连接"""
    print("🔍 测试Neo4j连接...")
    try:
        driver = GraphDatabase.driver(
            Config.NEO4J_URI,
            auth=(Config.NEO4J_USER, Config.NEO4J_PASSWORD)
        )
        
        with driver.session(database=Config.NEO4J_DATABASE) as session:
            result = session.run("RETURN 'Hello Neo4j!' as message")
            record = result.single()
            print(f"✓ Neo4j连接成功: {record['message']}")
            
            # 检查版本
            version_result = session.run("CALL dbms.components() YIELD name, versions")
            for record in version_result:
                if record["name"] == "Neo4j Kernel":
                    print(f"✓ Neo4j版本: {record['versions'][0]}")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Neo4j连接失败: {e}")
        return False

def test_openai_connection():
    """测试OpenAI连接"""
    print("\n🔍 测试OpenAI连接...")
    try:
        client = OpenAI(api_key=Config.OPENAI_API_KEY)
        
        # 简单的测试请求
        response = client.chat.completions.create(
            model=Config.MODEL_NAME,
            messages=[{"role": "user", "content": "Hello!"}],
            max_tokens=10
        )
        
        print(f"✓ OpenAI连接成功")
        print(f"✓ 使用模型: {Config.MODEL_NAME}")
        print(f"✓ 测试响应: {response.choices[0].message.content.strip()}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI连接失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 gyrw-kg 环境连接测试")
    print("=" * 50)
    
    # 验证配置
    try:
        Config.validate()
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # 测试连接
    neo4j_ok = test_neo4j_connection()
    openai_ok = test_openai_connection()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"Neo4j: {'✓ 正常' if neo4j_ok else '❌ 失败'}")
    print(f"OpenAI: {'✓ 正常' if openai_ok else '❌ 失败'}")
    
    if neo4j_ok and openai_ok:
        print("\n🎉 所有连接测试通过！可以开始使用pipeline了。")
        return 0
    else:
        print("\n⚠️  请检查配置并解决连接问题。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
