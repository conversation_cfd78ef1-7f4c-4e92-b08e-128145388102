"""
简单的Neo4j连接测试
"""
import os
from dotenv import load_dotenv
from neo4j import GraphDatabase

# 加载环境变量
load_dotenv()

def test_neo4j():
    uri = os.getenv("NEO4J_URI")
    user = os.getenv("NEO4J_USER") 
    password = os.getenv("NEO4J_PASSWORD")
    database = os.getenv("NEO4J_DATABASE")
    
    print(f"URI: {uri}")
    print(f"用户: {user}")
    print(f"密码: {'*' * len(password) if password else 'None'}")
    print(f"数据库: {database}")
    print()
    
    try:
        print("尝试连接...")
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        print("测试会话...")
        with driver.session(database=database) as session:
            result = session.run("RETURN 'Hello Neo4j!' as message")
            record = result.single()
            print(f"✓ 连接成功: {record['message']}")
            
            # 测试版本
            version_result = session.run("CALL dbms.components() YIELD name, versions")
            for record in version_result:
                if record["name"] == "Neo4j Kernel":
                    print(f"✓ Neo4j版本: {record['versions'][0]}")
        
        driver.close()
        print("✓ 连接测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

if __name__ == "__main__":
    test_neo4j()
